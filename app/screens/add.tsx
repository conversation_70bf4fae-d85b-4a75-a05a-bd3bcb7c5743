import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Platform, Alert, Keyboard, Modal, Image } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import { addFavorite, addTransaction, getFavorites, deleteFavorite, updateTransaction, getCategories, getMembers, getTags, updateFavoriteOrder, addCategory, getShoppingPlatforms } from '../constants/Storage';
import * as RevenueCat from '../utils/revenueCat';
import { Swipeable } from 'react-native-gesture-handler';
import { useTransactionContext } from '../context/TransactionContext';
import { Category } from './categories';
import { useCategoryContext } from '../context/CategoryContext';
import i18n from '../i18n';
import DateTimePicker from '@react-native-community/datetimepicker';
import DraggableFlatList, { ScaleDecorator } from 'react-native-draggable-flatlist';
import Animated from 'react-native-reanimated';
import { useSettings } from '../context/SettingsContext';
import EmptyState from '../components/EmptyState';
import { useTheme } from '../context/ThemeContext';

// 推荐分类数据 - 使用键值对应翻译
const RECOMMENDED_CATEGORIES = {
  expense: [
    { key: 'transportation', icon: '🚗' },
    { key: 'dining', icon: '🍽️' },
    { key: 'travel', icon: '✈️' },
    { key: 'shopping', icon: '🛒' },
    { key: 'entertainment', icon: '🎮' },
    { key: 'medical', icon: '🏥' },
    { key: 'education', icon: '📚' },
    { key: 'housing', icon: '🏠' },
    { key: 'communication', icon: '📱' },
    { key: 'clothing', icon: '👕' },
    { key: 'beauty', icon: '💄' },
    { key: 'sports', icon: '⚽' },
    { key: 'pets', icon: '🐕' },
    { key: 'gifts', icon: '🎁' },
    { key: 'insurance', icon: '🛡️' },
  ],
  income: [
    { key: 'salary', icon: '💰' },
    { key: 'bonus', icon: '🏆' },
    { key: 'investment', icon: '📈' },
    { key: 'partTime', icon: '💼' },
    { key: 'giftMoney', icon: '🧧' },
    { key: 'refund', icon: '💸' },
    { key: 'dividend', icon: '💹' },
    { key: 'rent', icon: '🏠' },
    { key: 'interest', icon: '🏦' },
    { key: 'other', icon: '💎' },
  ]
};

// 定义收藏记录的类型
export interface FavoriteRecord {
  id: number;
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  sort_order: number;
  member_id: number;
}

interface Tag {
  id: number;
  name: string;
  color: string;
}

interface Member {
  id: number;
  name: string;
}

const Add = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'income' | 'expense'>('expense');
  const [activeMode, setActiveMode] = useState<'new' | 'favorites'>('new');

  // 分离收入和支出的状态
  const [incomeState, setIncomeState] = useState({
    amount: '0.00',
    selectedCategory: 0,
    note: '',
    member_id: 0,
    refunded: false,
    refund_amount: 0,
    exclude_from_budget: false,
    image_path: '',
    reimbursement_status: 'none' as 'none' | 'pending' | 'completed',
  });

  const [expenseState, setExpenseState] = useState({
    amount: '0.00',
    selectedCategory: 0,
    note: '',
    member_id: 0,
    refunded: false,
    refund_amount: 0,
    exclude_from_budget: false,
    image_path: '',
    reimbursement_status: 'none' as 'none' | 'pending' | 'completed',
  });

  // 获取当前激活标签的状态和设置函数
  const currentState = activeTab === 'income' ? incomeState : expenseState;
  const setCurrentState = activeTab === 'income' ? setIncomeState : setExpenseState;

  // 金额输入相关状态
  const [isAmountFocused, setIsAmountFocused] = useState(false);
  const [showCalculator, setShowCalculator] = useState(false);

  // 处理金额输入变化
  const handleAmountChange = (value: string) => {
    // 允许输入数字、小数点和基本运算符
    const cleanValue = value.replace(/[^0-9+\-*/.]/g, '');
    updateCurrentState('amount', cleanValue);

    // 重置收藏选择标记，因为用户手动修改了金额
    if (justSelectedFromFavorites) {
      setJustSelectedFromFavorites(false);
    }
  };

  // 处理金额输入完成（失去焦点时）
  const handleAmountBlur = () => {
    setIsAmountFocused(false);
    const currentAmount = currentState.amount;

    // 如果包含运算符，尝试计算结果
    if (/[+\-*/]/.test(currentAmount)) {
      try {
        const result = Function('"use strict"; return (' + currentAmount + ')')();
        if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
          const formattedResult = Math.abs(result).toFixed(2);
          updateCurrentState('amount', formattedResult);
        } else {
          updateCurrentState('amount', '0.00');
        }
      } catch (error) {
        // 如果计算失败，保持原值或设为0
        updateCurrentState('amount', '0.00');
      }
    } else {
      // 如果只是数字，格式化为两位小数
      const numValue = parseFloat(currentAmount);
      if (!isNaN(numValue)) {
        updateCurrentState('amount', numValue.toFixed(2));
      } else {
        updateCurrentState('amount', '0.00');
      }
    }
  };

  // 图片选择功能
  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(i18n.t('common.error'), i18n.t('add.imagePermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;

        // 将图片复制到应用的文档目录
        const fileName = `transaction_${Date.now()}.jpg`;
        const newPath = `${FileSystem.documentDirectory}${fileName}`;
        await FileSystem.copyAsync({
          from: imageUri,
          to: newPath,
        });

        updateCurrentState('image_path', newPath);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.imagePickFailed'));
    }
  };

  // 删除图片
  const removeImage = async () => {
    try {
      if (currentState.image_path) {
        // 删除文件
        await FileSystem.deleteAsync(currentState.image_path, { idempotent: true });
        updateCurrentState('image_path', '');
      }
    } catch (error) {
      console.error('Error removing image:', error);
      updateCurrentState('image_path', '');
    }
  };

  // 计算器键盘功能
  const handleCalculatorInput = (value: string) => {
    const currentAmount = currentState.amount;

    if (value === 'clear') {
      updateCurrentState('amount', '');
    } else if (value === 'backspace') {
      updateCurrentState('amount', currentAmount.slice(0, -1));
    } else if (value === '=') {
      // 计算结果
      try {
        const result = Function('"use strict"; return (' + currentAmount + ')')();
        if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
          updateCurrentState('amount', Math.abs(result).toFixed(2));
        }
      } catch (error) {
        // 计算失败，保持原值
      }
    } else {
      // 添加数字或运算符
      if (currentAmount === '0.00' && /\d/.test(value)) {
        updateCurrentState('amount', value);
      } else {
        updateCurrentState('amount', currentAmount + value);
      }
    }
  };

  // 渲染计算器键盘
  const renderCalculator = () => {
    const buttons = [
      ['C', '⌫', '÷', '×'],
      ['7', '8', '9', '-'],
      ['4', '5', '6', '+'],
      ['1', '2', '3', '='],
      ['0', '.', '', '']
    ];

    const buttonMapping: {[key: string]: string} = {
      'C': 'clear',
      '⌫': 'backspace',
      '÷': '/',
      '×': '*',
      '=': '='
    };

    return (
      <View style={styles.calculatorContainer}>
        {buttons.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.calculatorRow}>
            {row.map((button, buttonIndex) => {
              if (button === '') return <View key={buttonIndex} style={styles.calculatorButton} />;

              const isOperator = ['C', '⌫', '÷', '×', '-', '+', '='].includes(button);
              const isZero = button === '0';

              return (
                <TouchableOpacity
                  key={buttonIndex}
                  style={[
                    styles.calculatorButton,
                    isOperator && styles.calculatorOperatorButton,
                    isZero && styles.calculatorZeroButton
                  ]}
                  onPress={() => handleCalculatorInput(buttonMapping[button] || button)}
                >
                  <Text style={[
                    styles.calculatorButtonText,
                    isOperator && styles.calculatorOperatorText
                  ]}>
                    {button}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  // 更新状态的辅助函数
  const updateCurrentState = (key: string, value: any) => {
    if (key === 'member_id') {
      // 如果点击当前已选中的成员，则取消选择（设置为0）
      if (currentState.member_id === value) {
        setCurrentState(prev => ({ ...prev, [key]: 0 }));
      } else {
        setCurrentState(prev => ({ ...prev, [key]: value }));
      }
    } else if (key === 'selectedCategory') {
      // 选择分类后自动折叠分类区域
      setCurrentState(prev => ({ ...prev, [key]: value }));
      if (value !== 0) {
        setCategoryExpanded(false);
      }
    } else if (key === 'refunded') {
      // 同时更新 currentState 和 isRefunded
      setCurrentState(prev => ({ ...prev, [key]: value }));
      setIsRefunded(value);
    } else if (key === 'excludeFromBudget') {
      // 更新 exclude_from_budget 字段
      setCurrentState(prev => ({ ...prev, exclude_from_budget: value }));
    } else if (key === 'reimbursement_status') {
      // 更新 reimbursement_status 字段
      setCurrentState(prev => ({
        ...prev,
        reimbursement_status: value,
        // 如果选择了报销，清除退款状态
        refunded: value !== 'none' ? false : prev.refunded,
        refund_amount: value !== 'none' ? 0 : prev.refund_amount
      }));
    } else {
      setCurrentState(prev => ({ ...prev, [key]: value }));
    }
  };

  // 收藏记录状态
  const [favorites, setFavorites] = useState<FavoriteRecord[]>([]);
  const { triggerRefresh } = useTransactionContext();
  const swipeableRefs = useRef<{ [key: number]: Swipeable | null }>({});
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const { refreshTrigger } = useCategoryContext();
  const [members, setMembers] = useState<Member[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [categoryExpanded, setCategoryExpanded] = useState(true);
  const [tagExpanded, setTagExpanded] = useState(false);
  const [shoppingPlatformExpanded, setShoppingPlatformExpanded] = useState(true);
  const noteInputRef = useRef<TextInput>(null);
  const amountInputRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const [showCategoryImportModal, setShowCategoryImportModal] = useState(false);
  const [selectedImportCategories, setSelectedImportCategories] = useState<string[]>([]);
  const [selectedShoppingPlatform, setSelectedShoppingPlatform] = useState<string>('');
  const [shoppingPlatforms, setShoppingPlatforms] = useState<any[]>([]);
  const [isPremium, setIsPremium] = useState(false);

  // 修改获取参数的部分
  const params = useLocalSearchParams() as any;
  const isEditing = params?.mode === 'edit';
  const [isRefunded, setIsRefunded] = useState(false);

  // 标记是否刚从收藏选择了金额
  const [justSelectedFromFavorites, setJustSelectedFromFavorites] = useState(false);

  const { currency, hideMemberSection, hideExcludeFromBudget, hideShoppingPlatformSection } = useSettings();
  const { theme } = useTheme();

  // 设置页面标题
  useEffect(() => {
    const title = isEditing ? i18n.t('add.editTitle') : i18n.t('add.title');
    navigation.setOptions({ title });
  }, [isEditing, navigation]);

  // 重置状态的函数
  const resetStates = () => {
    const defaultState = {
      amount: '0.00',
      selectedCategory: 0,
      note: '',
      member_id: 0,
      refunded: false,
      refund_amount: 0,
      exclude_from_budget: false,
      image_path: '',
      reimbursement_status: 'none' as 'none' | 'pending' | 'completed',
    };
    setIncomeState(defaultState);
    setExpenseState(defaultState);
    setSelectedTags([]);
    setSelectedShoppingPlatform('');
    setSelectedDate(new Date());
    setIsRefunded(false);
  };

  // 设置初始标签
  useEffect(() => {
    if (params?.mode === 'edit') {
      const targetStateFunc = params.type === 'income' ? setIncomeState : setExpenseState;
      const refundedValue = params.refunded === 'true';

      targetStateFunc({
        amount: params.amount || '0.00',
        selectedCategory: 0, // 将在类别加载后更新
        note: params.note || '',
        member_id: params.member_id || 0,
        refunded: refundedValue,
        refund_amount: parseFloat(params.refund_amount || '0'),
        exclude_from_budget: params.exclude_from_budget === 'true' ? true : false,
        image_path: params.image_path || '',
        reimbursement_status: (params.reimbursement_status as 'none' | 'pending' | 'completed') || 'none',
      });
      setActiveTab(params.type as 'income' | 'expense');
      setIsRefunded(refundedValue);

      // 编辑时默认折叠分类区域
      setCategoryExpanded(false);
      // 标签区域始终默认折叠
      setTagExpanded(true);

      if (params.date) {
        setSelectedDate(new Date(params.date));
      }

      if (params.category) {
        const findAndSetCategory = async () => {
          const cats = await getCategories(params.type);
          const cat = cats.find(c => c.name === params.category);
          if (cat) {
            targetStateFunc(prev => ({
              ...prev,
              selectedCategory: cat.id
            }));
          }
        };
        findAndSetCategory();
      }
      if (!!params.tags) {
        setSelectedTags(params.tags.split(',').map(Number));
      }
      if (params.member_id) {
        updateCurrentState('member_id', params.member_id)
        // setMembers(params.member_id)
      }
      if (params.shopping_platform) {
        setSelectedShoppingPlatform(params.shopping_platform);
      }

      // 如果来自分类选择页面，自动聚焦到金额输入框
      if (params.fromCategorySelect === 'true') {
        setTimeout(() => {
          amountInputRef.current?.focus();
        }, 500);
      }
    } else {
      // 非编辑模式下重置所有状态
      // resetStates();

      if (params?.initialTab) {
        setActiveTab(params.initialTab as 'income' | 'expense');
      }

      // 创建时默认展开分类区域
      setCategoryExpanded(true);
      // 标签区域始终默认折叠
      setTagExpanded(true);
    }
  }, []);

  // 加载类别数据
  const loadCategories = async () => {
    try {
      const data = await getCategories(activeTab);
      setCategories(data);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  // 当切换收入/支出标签时或类别更新时重新加载类别
  useEffect(() => {
    loadCategories();
    // 切换标签时重新展开分类区域（如果不是编辑模式）
    if (!isEditing) {
      setCategoryExpanded(true);
    }
    // 标签区域始终默认折叠
    setTagExpanded(true);
  }, [activeTab, refreshTrigger]);

  // 获取收藏列表
  const loadFavorites = async () => {
    try {
      const favs = await getFavorites(activeTab);
      setFavorites(favs);
    } catch (error) {
      console.error('Failed to load favorites:', error);
    }
  };

  // 当切换收入/支出标签时重新加载收藏
  useEffect(() => {
    loadFavorites();
  }, [activeTab]);

  // 添加到收藏
  const addToFavorites = async () => {
    // 验证输入
    if (!validateInput()) {
      return;
    }

    const category = categories.find(c => c.id === currentState.selectedCategory);
    if (!category) return;

    try {
      await addFavorite({
        type: activeTab,
        amount: parseFloat(currentState.amount),
        category: category.name,
        categoryIcon: category.icon,
        note: currentState.note,
        member_id: currentState.member_id,
      });

      // 重新加载收藏列表
      loadFavorites();

      setActiveMode('favorites');
    } catch (error) {
      console.error('Failed to add favorite:', error);
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return i18n.t('common.today');
    } else if (date.toDateString() === yesterday.toDateString()) {
      return i18n.t('common.yesterday');
    } else {
      // 根据当前语言格式化日期
      if (i18n.locale === 'zh') {
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
      } else {
        // 英文日期格式
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    }
  };

  // 修改保存函数，使用正确的日期格式
  const saveTransaction = async () => {
    // 验证输入
    if (!validateInput()) {
      return;
    }

    const category = categories.find(c => c.id === currentState.selectedCategory);
    if (!category) return;

    try {
      const transactionAmount = parseFloat(currentState.amount);
      const finalAmount = activeTab === 'income' ?
        Math.abs(transactionAmount) :
        -Math.abs(transactionAmount);

      // 格式化日期为 YYYY-MM-DD 格式
      const formattedDate = selectedDate.toISOString().split('T')[0];

      if (isEditing) {
        await updateTransaction(params.id, {
          type: activeTab,
          amount: finalAmount,
          category: category.name,
          categoryIcon: category.icon,
          note: currentState.note,
          date: formattedDate,
          member_id: currentState.member_id,
          refunded: currentState.refunded,
          exclude_from_budget: currentState.exclude_from_budget,
          shopping_platform: selectedShoppingPlatform || undefined,
          tags: selectedTags,
          image_path: currentState.image_path,
          reimbursement_status: currentState.reimbursement_status,
        });
      } else {
        await addTransaction({
          type: activeTab,
          amount: finalAmount,
          category: category.name,
          categoryIcon: category.icon,
          note: currentState.note,
          date: formattedDate,
          member_id: currentState.member_id,
          refunded: false,
          exclude_from_budget: currentState.exclude_from_budget,
          shopping_platform: selectedShoppingPlatform || undefined,
          tags: selectedTags,
          image_path: currentState.image_path || undefined,
          reimbursement_status: currentState.reimbursement_status,
        });
      }

      triggerRefresh();
      router.back();
    } catch (error) {
      console.error('Failed to save transaction:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.saveFailed'));
    }
  };

  const handleDeleteFavorite = async (id: number) => {
    try {
      await deleteFavorite(activeTab, id);
      loadFavorites(); // 重新加载收藏列表
    } catch (error) {
      console.error('Failed to delete favorite:', error);
    }
  };

  const closeAllSwipeables = () => {
    Object.values(swipeableRefs.current).forEach(ref => {
      ref?.close();
    });
  };

  useEffect(() => {
    const handleTouchStart = () => {
      closeAllSwipeables();
    };

  }, []);

  // 添加日期变更处理函数
  const onDateChange = (event: any, selectedDate?: Date) => {
    if (selectedDate) {
      setSelectedDate(selectedDate);
    }
    if (showDatePicker && Platform.OS === 'ios') {
      setShowDatePicker(false);
    }
  };



  // 验证输入数据
  const validateInput = () => {
    const amount = parseFloat(currentState.amount);

    // 检查金额
    if (!currentState.amount || currentState.amount === '0.00' || amount <= 0 || isNaN(amount)) {
      Alert.alert(
        i18n.t('common.error'),
        i18n.t('add.validation.amountRequired'),
        [{ text: i18n.t('common.ok') }]
      );
      return false;
    }

    // 检查分类
    if (!currentState.selectedCategory || currentState.selectedCategory === 0) {
      Alert.alert(
        i18n.t('common.error'),
        i18n.t('add.validation.categoryRequired'),
        [{ text: i18n.t('common.ok') }]
      );
      return false;
    }

    return true;
  };

  // 处理分类导入
  const handleImportCategories = async () => {
    if (selectedImportCategories.length === 0) {
      Alert.alert(i18n.t('common.error'), i18n.t('add.selectCategoriesToImport'));
      return;
    }

    try {
      const recommendedCats = RECOMMENDED_CATEGORIES[activeTab];
      for (const categoryName of selectedImportCategories) {
        // 根据本地化名称找到对应的分类数据
        const categoryData = recommendedCats.find(cat => {
          const localizedName = i18n.t(`add.recommendedCategories.${activeTab}.${cat.key}`);
          return localizedName === categoryName;
        });
        if (categoryData) {
          await addCategory({
            type: activeTab,
            name: categoryName, // 使用本地化的名称
            icon: categoryData.icon,
          });
        }
      }

      // 重新加载分类
      loadCategories();
      triggerRefresh();

      // 关闭模态框并重置选择
      setShowCategoryImportModal(false);
      setSelectedImportCategories([]);

      Alert.alert(i18n.t('common.ok'), i18n.t('add.categoriesImported'));
    } catch (error) {
      console.error('Failed to import categories:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.importFailed'));
    }
  };



  const renderTagSection = () => {
    return (
      <View>
        {/* 标签区域头部 */}
        <TouchableOpacity
          style={styles.categoryHeader}
          onPress={() => setTagExpanded(!tagExpanded)}
        >
          <Text style={styles.sectionTitle}>{i18n.t('tags.tags')}</Text>
          <View style={styles.categoryHeaderRight}>
            {selectedTags.length > 0 && (
              <View style={styles.selectedCategoryPreview}>
                <Text style={styles.selectedCategoryName}>
                  {i18n.t('tags.selectedTags', { count: selectedTags.length })}
                </Text>
              </View>
            )}
            <Ionicons
              name={tagExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color="#666"
            />
          </View>
        </TouchableOpacity>

        {/* 标签选择区域 */}
        {tagExpanded && (
          <View style={styles.tagGrid}>
            {tags.map(tag => (
              <TouchableOpacity
                key={tag.id}
                style={[
                  styles.tagItem,
                  selectedTags.includes(tag.id) && styles.selectedTag,
                  { borderColor: tag.color }
                ]}
                onPress={() => {
                  setSelectedTags((prev: any) => {
                    return prev?.includes(tag.id)
                      ? prev?.filter((id: any) => id !== tag.id)
                      : [...prev, tag.id];
                  });
                }}
              >
                <Text style={[
                  styles.tagText,
                  selectedTags.includes(tag.id) && { color: tag.color }
                ]}>{tag.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  }
  const renderCreateNew = () => (
    <ScrollView
      ref={scrollViewRef}
      style={styles.scrollView}
      contentContainerStyle={{ padding: 16, paddingBottom: 120 }}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >

      {/* 类别选择 */}
      <TouchableOpacity
        style={styles.categoryHeader}
        onPress={() => setCategoryExpanded(!categoryExpanded)}
      >
        <Text style={styles.sectionTitle}>{i18n.t('common.category')}</Text>
        <View style={styles.categoryHeaderRight}>
          {currentState.selectedCategory > 0 && (
            <View style={styles.selectedCategoryPreview}>
              <Text style={styles.selectedCategoryIcon}>
                {categories.find(c => c.id === currentState.selectedCategory)?.icon}
              </Text>
              <Text style={styles.selectedCategoryName}>
                {categories.find(c => c.id === currentState.selectedCategory)?.name}
              </Text>
            </View>
          )}
          <Ionicons
            name={categoryExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#666"
          />
        </View>
      </TouchableOpacity>

      {categoryExpanded && (
        <View style={styles.categoryGrid}>
          {categories.length === 0 ? (
            // 当没有分类时显示导入推荐分类的选项
            <TouchableOpacity
              style={[styles.categoryItem, styles.importCategoryItem]}
              onPress={() => setShowCategoryImportModal(true)}
            >
              <Ionicons name="download-outline" size={24} color="#4CAF50" />
              <Text style={[styles.categoryName, { color: '#4CAF50' }]}>
                {i18n.t('add.oneClickImport')}
              </Text>
            </TouchableOpacity>
          ) : (
            categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryItem,
                  currentState.selectedCategory === category.id && styles.selectedCategory
                ]}
                onPress={() => updateCurrentState('selectedCategory', category.id)}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text
                  style={styles.categoryName}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))
          )}
          <TouchableOpacity
            style={[styles.categoryItem, styles.otherCategoryItem]}
            onPress={() => router.push({
              pathname: '/screens/categories',
              params: { initialTab: activeTab }
            })}
          >
            <Ionicons name="add" size={24} color="#dc4446" />
            <Text style={[styles.categoryName, { color: '#dc4446' }]}>
              {i18n.t('common.add')}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 金额标题 */}
      <Text style={styles.sectionTitle}>{i18n.t('common.amount')}</Text>

      {/* 金额输入 */}
      <View style={styles.amountContainer}>
        <Text style={styles.currencySymbol}>{currency}</Text>
        <TouchableOpacity
          style={styles.amountInput}
          onPress={() => {
            setShowCalculator(true);
            setIsAmountFocused(true);
            // 如果刚从收藏选择了金额或金额为默认值，清空金额以便直接输入
            if (currentState.amount === '0.00' || justSelectedFromFavorites) {
              updateCurrentState('amount', '');
              if (justSelectedFromFavorites) {
                setJustSelectedFromFavorites(false);
              }
            }
          }}
        >
          <Text style={[styles.amountInputText, !currentState.amount && styles.amountPlaceholder]}>
            {currentState.amount || '0.00'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 日期选择 */}
      <Text style={styles.sectionTitle}>{i18n.t('common.date')}</Text>
      <TouchableOpacity
        style={styles.dateButton}
        onPress={() => setShowDatePicker(!showDatePicker)}
      >
        <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
        <Ionicons name="calendar-outline" size={20} color="#666" />
      </TouchableOpacity>

      {/* 日期选择器 */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'inline' : 'calendar'}
          onChange={onDateChange}
          textColor="#333"
          accentColor="#dc4446"
          themeVariant="light"
          maximumDate={new Date()}
          locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
        />
      )}

      {/* 备注输入 */}
      <Text style={styles.sectionTitle}>{i18n.t('common.note')}</Text>
      <TextInput
        ref={noteInputRef}
        style={styles.noteInput}
        placeholder={i18n.t('common.note')}
        value={currentState.note}
        onChangeText={(value) => updateCurrentState('note', value)}
        multiline
        onFocus={() => {
          // 当输入框获得焦点时，延迟滚动到输入框位置
          setTimeout(() => {
            noteInputRef.current?.measure((x, y, width, height, pageX, pageY) => {
              scrollViewRef.current?.scrollTo({
                y: pageY - 50, // 减少偏移量，确保输入框可见
                animated: true,
              });
            });
          }, 500); // 增加延迟时间，等待中文输入法完全弹出
        }}
        textAlignVertical="top"
        returnKeyType="done"
        blurOnSubmit={true}
      />

      {/* 图片上传 */}
      <Text style={styles.sectionTitle}>{i18n.t('add.image')}</Text>
      {!!currentState.image_path ? (
        <View style={styles.imageContainer}>
          <Image source={{ uri: currentState.image_path }} style={styles.selectedImage} />
          <TouchableOpacity style={styles.removeImageButton} onPress={removeImage}>
            <Ionicons name="close-circle" size={24} color="#FF5252" />
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity style={styles.imagePickerButton} onPress={pickImage}>
          <Ionicons name="camera-outline" size={24} color="#666" />
          <Text style={styles.imagePickerText}>{i18n.t('add.selectImage')}</Text>
        </TouchableOpacity>
      )}

      {/* 成员选择 */}
      {renderMemberSection()}

      {/* 不计入月预算选项 */}
      {!hideExcludeFromBudget && (activeTab === 'expense') && (
        <View style={styles.refundedContainer}>
          <TouchableOpacity
            style={styles.refundedOption}
            onPress={() => updateCurrentState('excludeFromBudget', !currentState.exclude_from_budget)}
          >
            <View style={styles.refundedCheckbox}>
              {currentState.exclude_from_budget && <Ionicons name="checkmark" size={18} color="#dc4446" />}
            </View>
            <Text style={styles.refundedText}>{i18n.t('common.excludeFromBudget')}</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 报销状态选择 - 只在支出时显示，与退款互斥 */}
      {activeTab === 'expense' && !isEditing && (
        <View>
          <Text style={styles.sectionTitle}>{i18n.t('reimbursement.reimbursementStatus')}</Text>
          <Text style={[styles.sectionSubtitle, { color: theme.textSecondary }]}>
            {i18n.t('reimbursement.reimbursementNote')}
          </Text>
          <View style={styles.reimbursementContainer}>
            <TouchableOpacity
              style={[
                styles.reimbursementOption,
                currentState.reimbursement_status === 'none' && styles.selectedReimbursementOption
              ]}
              onPress={() => updateCurrentState('reimbursement_status', 'none')}
            >
              <View style={styles.reimbursementCheckbox}>
                {currentState.reimbursement_status === 'none' && <Ionicons name="checkmark" size={18} color="#dc4446" />}
              </View>
              <Text style={[
                styles.reimbursementText,
                currentState.reimbursement_status === 'none' && styles.selectedReimbursementText
              ]}>
                {i18n.t('reimbursement.noReimbursement')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.reimbursementOption,
                currentState.reimbursement_status === 'pending' && styles.selectedReimbursementOption
              ]}
              onPress={() => updateCurrentState('reimbursement_status', 'pending')}
            >
              <View style={styles.reimbursementCheckbox}>
                {currentState.reimbursement_status === 'pending' && <Ionicons name="checkmark" size={18} color="#dc4446" />}
              </View>
              <Text style={[
                styles.reimbursementText,
                currentState.reimbursement_status === 'pending' && styles.selectedReimbursementText
              ]}>
                {i18n.t('reimbursement.needsReimbursement')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 退款状态 - 只读显示 */}
      {isEditing && activeTab === 'expense' && (currentState.refunded || currentState.refund_amount > 0) && (
        <View style={styles.refundedContainer}>
          <Text style={styles.sectionTitle}>{i18n.t('common.refundStatus')}</Text>
          <View style={styles.refundStatusDisplay}>
            {currentState.refunded && currentState.refund_amount >= Math.abs(parseFloat(currentState.amount)) ? (
              // 全额退款
              <View style={styles.refundStatusItem}>
                <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                <Text style={[styles.refundStatusText, { color: '#4CAF50' }]}>
                  {i18n.t('common.fullRefund')}: {currency}{Math.abs(parseFloat(currentState.amount)).toFixed(2)}
                </Text>
              </View>
            ) : currentState.refund_amount > 0 ? (
              // 部分退款
              <View style={styles.refundStatusItem}>
                <Ionicons name="remove-circle" size={20} color="#FF9800" />
                <Text style={[styles.refundStatusText, { color: '#FF9800' }]}>
                  {i18n.t('common.partialRefund')}: {currency}{currentState.refund_amount.toFixed(2)}
                </Text>
              </View>
            ) : null}
          </View>
        </View>
      )}

      {/* 购物平台选择 */}
      {renderShoppingPlatformSection()}

      {activeTab === 'expense' && tags.length > 0 && renderTagSection()}

    </ScrollView>
  );

  const renderFavorites = () => (
    <DraggableFlatList
      data={favorites}
      onDragEnd={async ({ data }) => {
        setFavorites(data);
        await updateFavoriteOrder(
          activeTab,
          data.map((item, index) => ({
            id: item.id,
            sort_order: index
          }))
        );
      }}
      keyExtractor={item => item.id.toString()}
      renderItem={({ item, drag, isActive }) => (
        <ScaleDecorator>
          <Animated.View>
            <Swipeable
              ref={(ref) => {
                swipeableRefs.current[item.id] = ref;
              }}
              renderRightActions={() => (
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => handleDeleteFavorite(item.id)}
                >
                  <Ionicons name="trash-outline" size={24} color="white" />
                </TouchableOpacity>
              )}
            >
              <TouchableOpacity
                style={[
                  styles.favoriteItem,
                  isActive && styles.activeFavoriteItem
                ]}
                onPress={() => {
                  // 设置当前状态
                  const category = categories.find(c => c.name === item.category);
                  if (category) {
                    updateCurrentState('selectedCategory', category.id);
                    updateCurrentState('amount', item.amount.toFixed(2));
                    updateCurrentState('note', item.note);
                    updateCurrentState('member_id', item.member_id);
                    setActiveMode('new');
                    // 标记刚从收藏选择了金额，以便下次点击金额输入框时可以直接替换
                    setJustSelectedFromFavorites(true);
                    // 从收藏选择后也要折叠分类区域和标签区域
                    setCategoryExpanded(false);
                    setTagExpanded(true);
                  }
                }}
                onLongPress={drag}
                delayLongPress={150}
              >
                <View style={styles.favoriteItemLeft}>
                  <View style={[
                    styles.favoriteCategoryIcon,
                    { backgroundColor: activeTab === 'income' ? '#FFF8E7' : '#FFF1F1' }
                  ]}>
                    <Text style={styles.favoriteCategoryIconText}>
                      {item.categoryIcon}
                    </Text>
                  </View>
                  <View style={styles.favoriteItemInfo}>
                    <Text style={styles.favoriteItemCategory}>{item.category}</Text>
                    {item.note && (
                      <Text style={styles.favoriteItemNote} numberOfLines={1}>
                        {item.note}
                      </Text>
                    )}
                    {item.member_id > 0 && (
                      <Text style={styles.favoriteItemMember}>
                        {members.find(m => m.id === item.member_id)?.name || ''}
                      </Text>
                    )}
                  </View>
                </View>
                <View style={styles.favoriteItemRight}>
                  <Text style={[
                    styles.favoriteItemAmount,
                    { color: activeTab === 'income' ? '#FF9A2E' : '#dc4446' }
                  ]}>
                    {activeTab === 'income' ? '+' : '-'}{currency}{item.amount.toFixed(2)}
                  </Text>
                  <Ionicons
                    name="menu"
                    size={18}
                    color="#999"
                    style={styles.dragHandle}
                  />
                </View>
              </TouchableOpacity>
            </Swipeable>
          </Animated.View>
        </ScaleDecorator>
      )}
      contentContainerStyle={styles.favoritesList}
      ListEmptyComponent={
        <EmptyState
          icon="star-outline"
          title={i18n.t('add.noFavorites')}
          description={i18n.t('add.clickAddButtonToCreate')}
        />
      }
    />
  );

  // 加载成员数据
  const loadMembers = async () => {
    try {
      const data = await getMembers();
      setMembers(data);
    } catch (error) {
      console.error('Failed to load members:', error);
    }
  };

  // 在组件加载时获取成员列表
  useEffect(() => {
    loadMembers();
  }, []);

  // 加载标签
  const loadTags = async () => {
    try {
      const data = await getTags();
      setTags(data);
    } catch (error) {
      console.error('Failed to load tags:', error);
    }
  };

  useEffect(() => {
    loadTags();
  }, []);

  // 加载购物平台
  const loadShoppingPlatforms = async () => {
    try {
      const data = await getShoppingPlatforms();
      setShoppingPlatforms(data);
    } catch (error) {
      console.error('Failed to load shopping platforms:', error);
    }
  };

  useEffect(() => {
    loadShoppingPlatforms();
    checkPremiumStatus();
  }, []);

  // 在购物平台数据加载完成后，重新设置选中状态
  useEffect(() => {
    if (isEditing && params.shopping_platform && shoppingPlatforms.length > 0) {
      setSelectedShoppingPlatform(params.shopping_platform);
    }
  }, [shoppingPlatforms, isEditing, params.shopping_platform]);

  // 检查会员状态
  const checkPremiumStatus = async () => {
    try {
      const premium = await RevenueCat.checkPremiumStatus();
      setIsPremium(premium);
    } catch (error) {
      console.error('Failed to check premium status:', error);
      setIsPremium(false);
    }
  };

  // 修改成员选择部分的渲染
  const renderMemberSection = () => {
    // 如果设置中隐藏了成员选择，则不显示
    if (hideMemberSection) return null;

    // 如果没有成员，则不显示
    if (members.length === 0) return null;

    return (
      <>
        <Text style={styles.sectionTitle}>{i18n.t('common.member')}</Text>
        <View style={styles.memberGrid}>
          {members.map(member => (
            <TouchableOpacity
              key={member.id}
              style={[
                styles.memberItem,
                (`${currentState.member_id}` === `${member.id}`) && styles.selectedMember
              ]}
              onPress={() => updateCurrentState('member_id', member.id)}
            >
              <Text style={[
                styles.memberText,
                (`${currentState.member_id}` === `${member.id}`) && styles.selectedMemberText
              ]}>
                {member.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </>
    );
  };

  // 购物平台选择部分的渲染
  const renderShoppingPlatformSection = () => {
    // 检查是否隐藏购物平台选择
    if (hideShoppingPlatformSection || (activeTab === 'income')) return null;

    // 只有会员才能看到购物平台选择
    // if (!isPremium) return null;

    // 如果没有购物平台，则不显示
    if (shoppingPlatforms.length === 0) return null;

    const selectedPlatform = shoppingPlatforms.find(p => p.name === selectedShoppingPlatform);

    return (
      <>
        {/* 购物平台选择头部 */}
        <TouchableOpacity
          style={styles.categoryHeader}
          onPress={() => setShoppingPlatformExpanded(!shoppingPlatformExpanded)}
        >
          <Text style={styles.sectionTitle}>{i18n.t('add.shoppingPlatform')}</Text>
          <View style={styles.categoryHeaderRight}>
            {selectedShoppingPlatform && selectedShoppingPlatform !== '' && (
              <View style={styles.selectedCategoryPreview}>
                <Text style={styles.selectedCategoryIcon}>
                  {selectedPlatform?.icon || '🛒'}
                </Text>
                <Text style={styles.selectedCategoryName}>
                  {selectedShoppingPlatform}
                </Text>
              </View>
            )}
            <Ionicons
              name={shoppingPlatformExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color="#666"
            />
          </View>
        </TouchableOpacity>

        {shoppingPlatformExpanded && (
          <View style={styles.memberGrid}>
            {/* 添加"无"选项 */}
            <TouchableOpacity
              style={[
                styles.memberItem,
                (!selectedShoppingPlatform || selectedShoppingPlatform === '') && styles.selectedMember
              ]}
              onPress={() => {
                setSelectedShoppingPlatform('');
                setShoppingPlatformExpanded(false);
              }}
            >
              <Text style={styles.platformIcon}>🚫</Text>
              <Text style={[
                styles.memberText,
                (!selectedShoppingPlatform || selectedShoppingPlatform === '') && styles.selectedMemberText
              ]}>
                {i18n.t('common.none')}
              </Text>
            </TouchableOpacity>

            {shoppingPlatforms.map(platform => (
              <TouchableOpacity
                key={platform.id}
                style={[
                  styles.memberItem,
                  selectedShoppingPlatform === platform.name && styles.selectedMember
                ]}
                onPress={() => {
                  setSelectedShoppingPlatform(platform.name);
                  setShoppingPlatformExpanded(false);
                }}
              >
                <Text style={styles.platformIcon}>{platform.icon}</Text>
                <Text style={[
                  styles.memberText,
                  selectedShoppingPlatform === platform.name && styles.selectedMemberText
                ]}>
                  {platform.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </>
    );
  };

  return (
    <View style={styles.container} onTouchStart={() => {
      closeAllSwipeables();
    }}>
      {/* 收入支出切换 */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'expense' && styles.activeTab]}
          onPress={() => setActiveTab('expense')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'expense' && styles.activeTabText
          ]}>{i18n.t('add.addExpense')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'income' && styles.activeTab]}
          onPress={() => setActiveTab('income')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'income' && styles.activeTabText
          ]}>{i18n.t('add.addIncome')}</Text>
        </TouchableOpacity>
      </View>

      {/* 创建模式切换 */}
      <View style={styles.modeContainer}>
        <TouchableOpacity
          style={[styles.modeButton, activeMode === 'new' && styles.activeModeButton]}
          onPress={() => setActiveMode('new')}
        >
          <Text style={[
            styles.modeText,
            activeMode === 'new' && styles.activeModeText
          ]}>{i18n.t('add.new')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.modeButton, activeMode === 'favorites' && styles.activeModeButton]}
          onPress={() => setActiveMode('favorites')}
        >
          <Text style={[
            styles.modeText,
            activeMode === 'favorites' && styles.activeModeText
          ]}>{i18n.t('add.favorites')}</Text>
        </TouchableOpacity>
      </View>

      {activeMode === 'new' ? renderCreateNew() : renderFavorites()}

      {/* 固定在底部的按钮组 */}
      {activeMode === 'new' && (
        <View style={styles.fixedButtonGroup}>
          <TouchableOpacity
            style={[styles.button, styles.favoriteButton]}
            onPress={addToFavorites}
          >
            <Ionicons name="star-outline" size={20} color="#dc4446" />
            <Text style={styles.favoriteButtonText}>{i18n.t('add.addToFavorites')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={saveTransaction}
          >
            <Text style={styles.saveButtonText}>{i18n.t('common.save')}</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 计算器键盘 Modal */}
      <Modal
        visible={showCalculator}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCalculator(false)}
      >
        <View style={styles.calculatorModal}>
          <TouchableOpacity
            style={styles.calculatorOverlay}
            onPress={() => setShowCalculator(false)}
          />
          <View style={styles.calculatorContent}>
            <View style={styles.calculatorHeader}>
              <Text style={styles.calculatorTitle}>{i18n.t('add.calculator')}</Text>
              <TouchableOpacity onPress={() => setShowCalculator(false)}>
                <Text style={styles.calculatorDone}>{i18n.t('add.calculatorClose')}</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.calculatorDisplay}>
              <Text style={styles.calculatorDisplayText}>{currentState.amount || '0'}</Text>
            </View>
            {renderCalculator()}
            <View style={styles.calculatorActions}>
              <TouchableOpacity
                style={styles.calculatorCloseButton}
                onPress={() => setShowCalculator(false)}
              >
                <Text style={styles.calculatorCloseButtonText}>{i18n.t('add.calculatorClose')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.calculatorSaveButton}
                onPress={() => {
                  setShowCalculator(false);
                  saveTransaction();
                }}
              >
                <Text style={styles.calculatorSaveButtonText}>{i18n.t('add.calculatorSave')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 分类导入模态框 */}
      <Modal
        visible={showCategoryImportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryImportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('add.selectCategoriesToImport')}</Text>
              <TouchableOpacity
                onPress={() => setShowCategoryImportModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.categoryImportList}>
              {RECOMMENDED_CATEGORIES[activeTab].map((category) => {
                const categoryName = i18n.t(`add.recommendedCategories.${activeTab}.${category.key}`);
                return (
                  <TouchableOpacity
                    key={category.key}
                    style={[
                      styles.categoryImportItem,
                      selectedImportCategories.includes(categoryName) && styles.selectedImportItem
                    ]}
                    onPress={() => {
                      if (selectedImportCategories.includes(categoryName)) {
                        setSelectedImportCategories(prev => prev.filter(name => name !== categoryName));
                      } else {
                        setSelectedImportCategories(prev => [...prev, categoryName]);
                      }
                    }}
                  >
                    <Text style={styles.categoryImportIcon}>{category.icon}</Text>
                    <Text style={styles.categoryImportName}>{categoryName}</Text>
                    {selectedImportCategories.includes(categoryName) && (
                      <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowCategoryImportModal(false)}
              >
                <Text style={styles.modalCancelText}>{i18n.t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalConfirmButton,
                  selectedImportCategories.length === 0 && styles.modalConfirmButtonDisabled
                ]}
                onPress={handleImportCategories}
                disabled={selectedImportCategories.length === 0}
              >
                <Text style={styles.modalConfirmText}>
                  {i18n.t('add.importSelected')} ({selectedImportCategories.length})
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 20,
  },
  scrollView: {
    flex: 1,
    paddingBottom: 40, // 增加底部内边距，确保内容不被键盘遮挡
  },
  tabs: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 5,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#dc4446',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  modeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  modeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: '#dc4446',
    alignItems: 'center',
  },
  activeModeButton: {
    backgroundColor: '#fff1f1',
  },
  modeText: {
    color: '#dc4446',
    fontSize: 16,
  },
  activeModeText: {
    fontWeight: '500',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  currencySymbol: {
    fontSize: 24,
    color: '#333',
    marginRight: 8,
  },
  amountInput: {
    fontSize: 24,
    color: '#333',
    flex: 1,
    justifyContent: 'center',
    minHeight: 40,
  },
  amountInputText: {
    fontSize: 24,
    color: '#333',
  },
  amountPlaceholder: {
    color: '#999',
  },

  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 12,
    marginBottom: 12,
    fontStyle: 'italic',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    marginBottom: 8,
  },
  categoryHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  selectedCategoryPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 4,
  },
  selectedCategoryIcon: {
    fontSize: 16,
  },
  selectedCategoryName: {
    fontSize: 14,
    color: '#4285f4',
    fontWeight: '500',
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    marginBottom: 16,
  },
  categoryItem: {
    width: '24%',
    // height: 1,
    // aspectRatio: 1,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 6,
  },
  otherCategoryItem: {
    backgroundColor: '#fff1f1',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#dc4446',
  },
  importCategoryItem: {
    backgroundColor: '#f1f8e9',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  selectedCategory: {
    backgroundColor: '#f0f6ff',
    borderWidth: 1,
    borderColor: '#4285f4',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 11,
    color: '#666',
    textAlign: 'center',
    width: '100%',
    paddingHorizontal: 1,
    lineHeight: 14,
    marginTop: 2,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  noteInput: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    fontSize: 16,
    color: '#333',
    minHeight: 80, // 减少最小高度，保持紧凑
    maxHeight: 120, // 减少最大高度
    textAlignVertical: 'top',
    lineHeight: 24, // 增加行高，改善中文显示
    paddingTop: 16, // 确保文本从顶部开始
    paddingBottom: 16, // 增加底部内边距
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
    marginBottom: 20, // 确保按钮下方有足够空间
  },
  fixedButtonGroup: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  button: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  favoriteButton: {
    backgroundColor: '#fff1f1',
  },
  favoriteButtonText: {
    color: '#dc4446',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#dc4446',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  // favoriteItem: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   backgroundColor: 'white',
  //   padding: 16,
  //   marginVertical: 6,
  //   borderRadius: 12,
  //   shadowColor: '#000',
  //   shadowOffset: { width: 0, height: 1 },
  //   shadowOpacity: 0.1,
  //   shadowRadius: 2,
  //   elevation: 2,
  // },
  // activeFavoriteItem: {
  //   backgroundColor: '#f9f9f9',
  //   shadowOpacity: 0.2,
  //   elevation: 3,
  // },
  // favoriteItemLeft: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   flex: 1,
  // },
  // favoriteCategoryIcon: {
  //   width: 44,
  //   height: 44,
  //   borderRadius: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   marginRight: 12,
  // },
  // favoriteCategoryIconText: {
  //   fontSize: 20,
  // },
  // favoriteItemInfo: {
  //   flex: 1,
  // },
  // favoriteItemCategory: {
  //   fontSize: 16,
  //   fontWeight: '500',
  //   color: '#333',
  //   marginBottom: 4,
  // },
  // favoriteItemNote: {
  //   fontSize: 14,
  //   color: '#666',
  //   marginBottom: 2,
  // },
  // favoriteItemMember: {
  //   fontSize: 12,
  //   color: '#666',
  //   backgroundColor: '#f5f5f5',
  //   paddingVertical: 2,
  //   paddingHorizontal: 6,
  //   borderRadius: 4,
  //   alignSelf: 'flex-start',
  // },
  // favoriteItemRight: {
  //   alignItems: 'flex-end',
  //   flexDirection: 'row',
  //   gap: 8,
  // },
  // favoriteItemAmount: {
  //   fontSize: 16,
  //   fontWeight: '600',
  // },
  // dragHandle: {
  //   padding: 4,
  // },
  // deleteButton: {
  //   backgroundColor: '#dc4446',
  //   width: 70,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   height: '100%',
  //   borderTopRightRadius: 12,
  //   borderBottomRightRadius: 12,
  // },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    color: '#666',
    fontSize: 16,
  },
  excludeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 24,
  },
  excludeCheckbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#dc4446',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  excludeText: {
    fontSize: 16,
    color: '#333',
  },
  memberGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  memberItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedMember: {
    backgroundColor: '#fff1f1',
    borderColor: '#dc4446',
  },
  memberText: {
    fontSize: 14,
    color: '#666',
  },
  selectedMemberText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  platformIcon: {
    fontSize: 16,
    marginBottom: 4,
    textAlign: 'center',
  },
  refundedContainer: {
    marginBottom: 24,
  },
  refundedOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  refundedCheckbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#dc4446',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  refundedText: {
    fontSize: 16,
    color: '#333',
  },
  refundedBadge: {
    backgroundColor: '#FFF1F1',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8,
  },
  refundedBadgeText: {
    color: '#dc4446',
    fontSize: 12,
  },
  refundAmountDisplay: {
    marginTop: 8,
    paddingLeft: 36, // Align with the checkbox text
  },
  refundAmountText: {
    fontSize: 14,
    color: '#666',
  },
  refundStatusDisplay: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  refundStatusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  refundStatusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  refundStatusNote: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 4,
  },
  tagGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  tagItem: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedTag: {
    backgroundColor: '#fff1f1',
  },
  tagText: {
    fontSize: 14,
    color: '#666',
  },
  favoritesList: {
    // paddingHorizontal: 16,
    paddingBottom: 100,
  },
  favoriteItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    marginVertical: 6,
    borderRadius: 12,
    // shadowColor: '#000',
    shadowColor: 'red',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activeFavoriteItem: {
    backgroundColor: '#f9f9f9',
    shadowOpacity: 0.2,
    elevation: 3,
  },
  favoriteItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  favoriteCategoryIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  favoriteCategoryIconText: {
    fontSize: 20,
  },
  favoriteItemInfo: {
    flex: 1,
  },
  favoriteItemCategory: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  favoriteItemNote: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  favoriteItemMember: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f5f5f5',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  favoriteItemRight: {
    alignItems: 'flex-end',
    flexDirection: 'row',
    gap: 8,
  },
  favoriteItemAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  dragHandle: {
    // padding: 4,
  },
  deleteButton: {
    backgroundColor: '#dc4446',
    width: 70,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalCloseButton: {
    padding: 4,
  },
  categoryImportList: {
    maxHeight: 400,
    paddingHorizontal: 20,
  },
  categoryImportItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
  },
  selectedImportItem: {
    backgroundColor: '#e8f5e8',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  categoryImportIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  categoryImportName: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  modalActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: 16,
    color: '#666',
  },
  modalConfirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
  },
  modalConfirmButtonDisabled: {
    backgroundColor: '#ccc',
  },
  modalConfirmText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  selectedImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    gap: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  imagePickerText: {
    fontSize: 16,
    color: '#666',
  },
  calculatorModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  calculatorOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  calculatorContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  calculatorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  calculatorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  calculatorDone: {
    fontSize: 16,
    color: '#dc4446',
    fontWeight: '500',
  },
  calculatorDisplay: {
    padding: 20,
    alignItems: 'flex-end',
    backgroundColor: '#f8f8f8',
  },
  calculatorDisplayText: {
    fontSize: 32,
    fontWeight: '300',
    color: '#333',
  },
  calculatorContainer: {
    padding: 10,
  },
  calculatorRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  calculatorButton: {
    flex: 1,
    height: 60,
    margin: 5,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calculatorOperatorButton: {
    backgroundColor: '#dc4446',
  },
  calculatorZeroButton: {
    flex: 2,
  },
  calculatorButtonText: {
    fontSize: 24,
    fontWeight: '400',
    color: '#333',
  },
  calculatorOperatorText: {
    color: 'white',
    fontWeight: '500',
  },
  calculatorActions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  calculatorCloseButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calculatorCloseButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  calculatorSaveButton: {
    flex: 2,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#dc4446',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calculatorSaveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  reimbursementContainer: {
    marginBottom: 16,
  },
  reimbursementOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedReimbursementOption: {
    backgroundColor: '#fff1f1',
    borderColor: '#dc4446',
  },
  reimbursementCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#dc4446',
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reimbursementText: {
    fontSize: 16,
    color: '#333',
  },
  selectedReimbursementText: {
    color: '#dc4446',
    fontWeight: '500',
  },
});

export default Add;